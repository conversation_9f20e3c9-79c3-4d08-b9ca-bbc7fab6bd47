-- Migration: Add RLS policies for analytics tables
-- Date: 2025-07-19
-- Description: Add RLS policies for visitor_sessions and page_visits tables to allow proper access

-- Enable RLS on analytics tables if not already enabled
ALTER TABLE visitor_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE page_visits ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Allow visitor session tracking" ON visitor_sessions;
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all visitor sessions" ON visitor_sessions;
DROP POLICY IF EXISTS "Allow page visit tracking" ON page_visits;
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all page visits" ON page_visits;

-- Visitor sessions policies
-- Allow anyone to insert visitor sessions (for tracking)
CREATE POLICY "Allow visitor session tracking" ON visitor_sessions
    FOR INSERT WITH CHECK (true);

-- Allow anyone to update visitor sessions (for tracking updates)
CREATE POLICY "Allow visitor session updates" ON visitor_sessions
    FOR UPDATE USING (true);

-- <PERSON><PERSON> can view all visitor sessions
CREATE POLICY "Ad<PERSON> can view all visitor sessions" ON visitor_sessions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND is_admin = true
        )
    );

-- Page visits policies
-- Allow anyone to insert page visits (for tracking)
CREATE POLICY "Allow page visit tracking" ON page_visits
    FOR INSERT WITH CHECK (true);

-- Allow anyone to update page visits (for duration tracking)
CREATE POLICY "Allow page visit updates" ON page_visits
    FOR UPDATE USING (true);

-- Admins can view all page visits
CREATE POLICY "Admins can view all page visits" ON page_visits
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND is_admin = true
        )
    );

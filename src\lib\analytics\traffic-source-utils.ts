/**
 * Utility functions for traffic source categorization
 */

/**
 * Categorize traffic sources based on referrer URL
 */
export function categorizeTrafficSource(referrer: string): string {
  if (!referrer || referrer === '') return 'direct'
  
  const url = referrer.toLowerCase()
  
  // Search engines
  if (url.includes('google.') || url.includes('bing.') || url.includes('yahoo.') ||
      url.includes('duckduckgo.') || url.includes('baidu.') || url.includes('yandex.')) {
    return 'search'
  }
  
  // Social media - flexible pattern matching
  if (url.includes('facebook') || url.includes('instagram') || url.includes('twitter') ||
      url.includes('linkedin') || url.includes('pinterest') || url.includes('tiktok') ||
      url.includes('youtube') || url.includes('whatsapp') || url.includes('telegram') ||
      url.includes('t.co') || url.includes('fb.me') || url.includes('bit.ly')) {
    return 'social'
  }
  
  // Email
  if (url.includes('mail.') || url.includes('gmail.') || url.includes('outlook.') ||
      url.includes('yahoo.') || url.includes('webmail.') || url.includes('email.')) {
    return 'email'
  }
  
  // Same domain (internal)
  if (url.includes('primecaffe.ch') || url.includes('localhost')) {
    return 'internal'
  }
  
  // Everything else is referral
  return 'referral'
}

/**
 * Get traffic source display information
 */
export function getTrafficSourceInfo(source: string, t: (key: string) => string): { name: string; icon: string } {
  const sourceMap: Record<string, { name: string; icon: string }> = {
    'direct': { name: t('analytics.trafficSource.direct') || 'Diretto', icon: '🔗' },
    'search': { name: t('analytics.trafficSource.search') || 'Motori di Ricerca', icon: '🔍' },
    'social': { name: t('analytics.trafficSource.social') || 'Social Media', icon: '📱' },
    'email': { name: t('analytics.trafficSource.email') || 'Email', icon: '📧' },
    'referral': { name: t('analytics.trafficSource.referral') || 'Referral', icon: '🌐' },
    'internal': { name: t('analytics.trafficSource.internal') || 'Interno', icon: '🏠' }
  }
  return sourceMap[source] || { name: source, icon: '🌐' }
}
